/**
 * @file ECPPrivateKeyValidate.cpp
 * @brief RF Online ECP Private Key Validation Function
 * @note Original Function: ?Validate@?$DL_PrivateKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x1404515F0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateDL_PrivateKeyImplVDL_GroupParameters_ECVEC_1404515F0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

/**
 * @brief Validates an ECP private key implementation
 * @param this Pointer to the ECP private key implementation
 * @param rng Random number generator for validation
 * @param level Validation level (0=basic, 1=thorough)
 * @return true if the private key is valid, false otherwise
 *
 * This function validates an elliptic curve private key over prime fields (ECP).
 * It performs basic range checks and optionally more thorough mathematical validation.
 */
bool ECPPrivateKey_Validate(
    CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>* this,
    CryptoPP::RandomNumberGenerator* rng,
    unsigned int level) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;               // Original: v3 (rdi register)
    signed __int64 loopCounter;           // Original: i (rcx register)
    __int64 groupParamsPtr;               // Original: v5 (rax register)
    __int64 groupParamsPtr2;              // Original: v6 (rax register)
    CryptoPP::Integer* orderPtr;          // Original: v7 (rax register)
    CryptoPP::Integer* privateKeyPtr;     // Original: v8 (rax register)
    __int64 stackBuffer[48];              // Original: v10 (stack buffer [sp+0h] [bp-C8h])
    bool isValid;                         // Original: v11 ([sp+20h] [bp-A8h])
    CryptoPP::Integer* groupOrder;        // Original: b ([sp+28h] [bp-A0h])
    CryptoPP::Integer* privateKeyValue;   // Original: a ([sp+30h] [bp-98h])
    __int64 groupParameters;              // Original: v14 ([sp+38h] [bp-90h])
    CryptoPP::Integer gcdResult;          // Original: v15 ([sp+40h] [bp-88h])
    int constructionFlags;                // Original: v16 ([sp+68h] [bp-60h])
    __int64 framePointer;                 // Original: v17 ([sp+70h] [bp-58h])
    CryptoPP::ASN1ObjectVtbl* vtable1;    // Original: v18 ([sp+78h] [bp-50h])
    CryptoPP::ASN1ObjectVtbl* vtable2;    // Original: v19 ([sp+80h] [bp-48h])
    __int64 groupParameters2;             // Original: v20 ([sp+88h] [bp-40h])
    CryptoPP::ASN1ObjectVtbl* vtable3;    // Original: v21 ([sp+90h] [bp-38h])
    int basicValidation;                  // Original: v22 ([sp+98h] [bp-30h])
    CryptoPP::Integer* oneValue;          // Original: v23 ([sp+A0h] [bp-28h])
    CryptoPP::Integer* gcdResultPtr;      // Original: v24 ([sp+A8h] [bp-20h])
    CryptoPP::Integer* gcdResultPtr2;     // Original: v25 ([sp+B0h] [bp-18h])
    int thoroughValidation;               // Original: v26 ([sp+B8h] [bp-10h])
    CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>* currentKey; // Original: v27 ([sp+D0h] [bp+8h])
    CryptoPP::RandomNumberGenerator* randomGen;  // Original: v28 ([sp+D8h] [bp+10h])
    unsigned int validationLevel;        // Original: v29 ([sp+E0h] [bp+18h])

    // Initialize parameters
    validationLevel = level;
    randomGen = rng;
    currentKey = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 48; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Initialize frame pointer and construction flags
    framePointer = -2LL;
    constructionFlags = 0;
    
    // Get group parameters from the private key
    vtable1 = currentKey[-1].vfptr;
    groupParamsPtr = reinterpret_cast<__int64>(
        reinterpret_cast<int(*)(CryptoPP::ASN1ObjectVtbl**)>(vtable1->__vecDelDtor)
        (&currentKey[-1].vfptr)
    );
    groupParameters = groupParamsPtr;
    
    // Validate group parameters using the random number generator
    isValid = (*reinterpret_cast<int(**)(
        __int64, 
        CryptoPP::RandomNumberGenerator*, 
        QWORD
    )>(
        *reinterpret_cast<QWORD*>(groupParamsPtr + 
            *reinterpret_cast<DWORD*>(*reinterpret_cast<QWORD*>(groupParamsPtr + 8) + 4LL) + 8) + 24LL
    ))(
        groupParameters + *reinterpret_cast<DWORD*>(*reinterpret_cast<QWORD*>(groupParameters + 8) + 4LL) + 8,
        randomGen,
        validationLevel
    );
    
    // Get group parameters again for order extraction
    vtable2 = currentKey[-1].vfptr;
    groupParamsPtr2 = reinterpret_cast<__int64>(
        reinterpret_cast<int(*)(CryptoPP::ASN1ObjectVtbl**)>(vtable2->__vecDelDtor)
        (&currentKey[-1].vfptr)
    );
    groupParameters2 = groupParamsPtr2;
    
    // Get the group order
    orderPtr = reinterpret_cast<CryptoPP::Integer*>(
        (*reinterpret_cast<int(**)(
            __int64
        )>(*reinterpret_cast<QWORD*>(groupParameters2) + 64LL))(groupParameters2)
    );
    groupOrder = orderPtr;
    
    // Get the private key value
    vtable3 = currentKey[-1].vfptr;
    privateKeyPtr = reinterpret_cast<CryptoPP::Integer*>(
        reinterpret_cast<int(*)(signed __int64)>(vtable3->DEREncode)
        (reinterpret_cast<signed __int64>(&currentKey[-1].vfptr))
    );
    privateKeyValue = privateKeyPtr;
    
    // Perform basic validation: check if private key is positive and less than group order
    basicValidation = isValid && 
                     CryptoPP::Integer::IsPositive(privateKeyValue) && 
                     CryptoPP::operator<(privateKeyValue, groupOrder);
    isValid = basicValidation;
    
    // If thorough validation is requested (level >= 1)
    if (validationLevel >= 1) {
        // Perform additional validation: check if gcd(private_key, group_order) == 1
        thoroughValidation = isValid && (
            oneValue = reinterpret_cast<CryptoPP::Integer*>(CryptoPP::Integer::One()),
            gcdResultPtr = CryptoPP::Integer::Gcd(&gcdResult, privateKeyValue, groupOrder),
            gcdResultPtr2 = gcdResultPtr,
            constructionFlags |= 1u,
            CryptoPP::operator==(gcdResultPtr, oneValue)
        );
        isValid = thoroughValidation;
        
        // Clean up the GCD result if it was constructed
        if (constructionFlags & 1) {
            constructionFlags &= 0xFFFFFFFE;
            CryptoPP::Integer::~Integer(&gcdResult);
        }
    }
    
    return isValid;
}

/*
 * MoveMapLimitRightDestructor3.cpp
 * Original Function: CMoveMapLimitRightInfo::LogIn_::_1_::dtor$3
 * Address: 0x1403AD120
 *
 * Exception unwinding destructor helper 3 for move map limit right info.
 * Destroys vector const iterator during exception handling.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>
#include <vector>

/**
 * Exception unwinding destructor helper 3
 * Destroys vector const iterator during exception handling
 * @param a1 Exception context parameter
 * @param a2 Object pointer containing iterator at offset 104
 */
void MoveMapLimitRightDestructor3(__int64 a1, __int64 a2)
{
    // Destroy vector const iterator at offset 104
    MoveMapLimitRightVectorConstIteratorDestructor((void*)(a2 + 104));
}

/*
 * AsyncLogListIteratorConstruct.cpp
 * Original Function: std::_Construct<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>>
 * Address: 0x1403C8B70
 *
 * STL _Construct function for async log list iterators.
 * Constructs a list iterator using placement new and copy construction.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * STL _Construct function for async log list iterators
 * Constructs a list iterator using placement new and copy construction
 * @param _Ptr Pointer to location where to construct the iterator
 * @param _Val Pointer to iterator to copy from
 */
void AsyncLogListIteratorConstruct(void* _Ptr, const void* _Val)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-58h]@1
    void* _Where; // [sp+20h] [bp-38h]@4
    void* v6; // [sp+30h] [bp-28h]@4
    __int64 v7; // [sp+38h] [bp-20h]@4
    void* v8; // [sp+60h] [bp+8h]@1
    const void* __that; // [sp+68h] [bp+10h]@1

    __that = _Val;
    v8 = _Ptr;
    v2 = &v4;
    
    // Initialize debug pattern in local memory
    for (i = 20LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    v7 = -2LL;
    _Where = v8;
    v6 = operator new(0x18ui64, v8);
    
    // Construct the iterator if allocation succeeded
    if (v6) {
        AsyncLogListIteratorCopyConstructor(v6, __that);
    }
}

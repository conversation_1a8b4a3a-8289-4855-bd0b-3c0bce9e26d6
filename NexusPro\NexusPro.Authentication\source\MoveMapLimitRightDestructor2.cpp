/*
 * MoveMapLimitRightDestructor2.cpp
 * Original Function: CMoveMapLimitRightInfo::LogIn_::_1_::dtor$2
 * Address: 0x1403AD0F0
 *
 * Exception unwinding destructor helper 2 for move map limit right info.
 * Destroys vector iterator during exception handling.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>
#include <vector>

/**
 * Exception unwinding destructor helper 2
 * Destroys vector iterator during exception handling
 * @param a1 Exception context parameter
 * @param a2 Object pointer containing iterator at offset 168
 */
void MoveMapLimitRightDestructor2(__int64 a1, __int64 a2)
{
    // Destroy vector iterator at offset 168
    MoveMapLimitRightVectorIteratorDestructor((void*)(a2 + 168));
}

/*
 * AsyncLogPairConstruct.cpp
 * Original Function: std::_Construct<std::pair<int const,CAsyncLogInfo*>>
 * Address: 0x1403C7DB0
 *
 * STL _Construct function for async log pair objects.
 * Constructs a pair using placement new and copy construction.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// Additional STL includes for compilation
#include <utility>

/**
 * STL _Construct function for async log pair objects
 * Constructs a pair using placement new and copy construction
 * @param _Ptr Pointer to location where to construct the object
 * @param _Val Pointer to value to copy from
 */
void AsyncLogPairConstruct(AsyncLogPair* _Ptr, const AsyncLogPair* _Val)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-68h]@1
    void* _Where; // [sp+20h] [bp-48h]@4
    char* v6; // [sp+28h] [bp-40h]@4
    char v7[16]; // [sp+30h] [bp-38h]@5
    AsyncLogPair* v8; // [sp+70h] [bp+8h]@1
    const AsyncLogPair* v9; // [sp+78h] [bp+10h]@1

    v9 = _Val;
    v8 = _Ptr;
    v2 = &v4;
    
    // Initialize debug pattern in local memory
    for (i = 22LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    _Where = v8;
    v6 = (char*)operator new(0x10ui64, v8);
    
    // Construct the object if allocation succeeded
    if (v6) {
        // Copy the source pair to temporary buffer
        memcpy(&v7, v9, 0x10ui64);
        // Copy from temporary buffer to destination
        memcpy(v6, &v7, 0x10ui64);
    }
}

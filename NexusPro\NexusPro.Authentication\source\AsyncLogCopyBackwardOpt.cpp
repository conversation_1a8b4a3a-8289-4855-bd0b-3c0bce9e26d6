/*
 * AsyncLogCopyBackwardOpt.cpp
 * Original Function: std::_Copy_backward_opt<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>*>
 * Address: 0x1403C8AD0
 *
 * STL _Copy_backward_opt function for async log list iterators.
 * Copies elements backward from source range to destination.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * STL _Copy_backward_opt function for async log list iterators
 * Copies elements backward from source range to destination
 * @param _First Iterator to beginning of source range
 * @param _Last Iterator to end of source range
 * @param _Dest Iterator to end of destination range
 * @param __formal Random access iterator tag (unused)
 * @param a5 Nonscalar pointer iterator tag (unused)
 * @param a6 Range checked iterator tag (unused)
 * @return Iterator to beginning of copied range in destination
 */
void* AsyncLogCopyBackwardOpt(void* _First, void* _Last, void* _Dest, 
                              int __formal, int a5, int a6)
{
    __int64* v6; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v9; // [sp+0h] [bp-28h]@1
    void* v10; // [sp+30h] [bp+8h]@1
    void* __that; // [sp+38h] [bp+10h]@1
    void* v12; // [sp+40h] [bp+18h]@1

    v12 = _Dest;
    __that = _Last;
    v10 = _First;
    v6 = &v9;
    
    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v6 = 0xCCCCCCCC;
        v6 = (__int64*)((char*)v6 + 4);
    }
    
    // Copy elements backward from source to destination
    while (v10 != __that) {
        AsyncLogListIteratorDecrement(__that);
        AsyncLogListIteratorDecrement(v12);
        AsyncLogListIteratorAssignment(v12, __that);
    }
    
    return v12;
}

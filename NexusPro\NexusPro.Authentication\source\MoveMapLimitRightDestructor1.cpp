/*
 * MoveMapLimitRightDestructor1.cpp
 * Original Function: CMoveMapLimitRightInfo::LogIn_::_1_::dtor$1
 * Address: 0x1403AD0C0
 *
 * Exception unwinding destructor helper 1 for move map limit right info.
 * Destroys vector const iterator during exception handling.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>
#include <vector>

/**
 * Exception unwinding destructor helper 1
 * Destroys vector const iterator during exception handling
 * @param a1 Exception context parameter
 * @param a2 Object pointer containing iterator at offset 56
 */
void MoveMapLimitRightDestructor1(__int64 a1, __int64 a2)
{
    // Destroy vector const iterator at offset 56
    MoveMapLimitRightVectorConstIteratorDestructor((void*)(a2 + 56));
}

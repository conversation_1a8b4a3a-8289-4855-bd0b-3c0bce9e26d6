/*
 * AsyncLogVectorBuy.cpp
 * Original Function: std::vector<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>>::_Buy
 * Address: 0x1403C7100
 *
 * STL vector _Buy operation for async log containers.
 * Allocates memory for vector storage and initializes pointers.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <vector>

/**
 * Vector _Buy operation - allocates memory for vector storage
 * @param this Pointer to the vector object
 * @param _Capacity Number of elements to allocate space for
 * @return true if allocation successful, false otherwise
 */
bool AsyncLogVectorBuy(void* this_ptr, unsigned __int64 _Capacity)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    bool result; // al@5
    void* v5; // rcx@6
    __int64 v6; // [sp+0h] [bp-28h]@1
    void* v7; // [sp+30h] [bp+8h]@1
    unsigned __int64 _Count; // [sp+38h] [bp+10h]@1

    _Count = _Capacity;
    v7 = this_ptr;
    v2 = &v6;
    
    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    // Initialize vector pointers to null
    ((AsyncLogVector*)v7)->_Myfirst = nullptr;
    ((AsyncLogVector*)v7)->_Mylast = nullptr;
    ((AsyncLogVector*)v7)->_Myend = nullptr;
    
    if (_Capacity) {
        // Check if capacity exceeds maximum size
        if (AsyncLogVectorMaxSize(v7) < _Capacity) {
            AsyncLogVectorThrowLengthError(v5);
        }
        
        // Allocate memory for the vector
        ((AsyncLogVector*)v7)->_Myfirst = AsyncLogVectorAllocate(
            &((AsyncLogVector*)v7)->_Alval,
            _Count);
        ((AsyncLogVector*)v7)->_Mylast = ((AsyncLogVector*)v7)->_Myfirst;
        ((AsyncLogVector*)v7)->_Myend = &((AsyncLogVector*)v7)->_Myfirst[_Count];
        result = true;
    }
    else {
        result = false;
    }
    return result;
}

/*
 * AsyncLogListNodeConstruct.cpp
 * Original Function: std::_Construct<std::_List_nod<std::pair<int const,CAsyncLogInfo*>>::_Node*>
 * Address: 0x1403C7F50
 *
 * STL _Construct function for async log list node pointers.
 * Constructs a list node pointer using placement new.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>
#include <list>

/**
 * STL _Construct function for list node pointers
 * Constructs a list node pointer using placement new
 * @param _Ptr Pointer to location where to construct the object
 * @param _Val Pointer to value to copy from
 */
void AsyncLogListNodeConstruct(void** _Ptr, void* const* _Val)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-48h]@1
    void* _Where; // [sp+20h] [bp-28h]@4
    void** v6; // [sp+28h] [bp-20h]@4
    void** v7; // [sp+50h] [bp+8h]@1
    void* const* v8; // [sp+58h] [bp+10h]@1

    v8 = _Val;
    v7 = _Ptr;
    v2 = &v4;
    
    // Initialize debug pattern in local memory
    for (i = 16LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }
    
    _Where = v7;
    v6 = (void**)operator new(8ui64, v7);
    
    // Construct the object if allocation succeeded
    if (v6) {
        *v6 = *v8;
    }
}

/*
 * AsyncLogVectorConstructN.cpp
 * Original Function: std::vector<std::list<std::pair<int const,CAsyncLogInfo*>>::_Iterator<0>>::_Construct_n
 * Address: 0x1403C6820
 *
 * STL vector _Construct_n function for async log containers.
 * Constructs n elements in the vector with a given value.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * Vector _Construct_n function - constructs n elements with given value
 * @param this Pointer to the vector object
 * @param _Count Number of elements to construct
 * @param _Val Pointer to value to use for construction
 */
void AsyncLogVectorConstructN(void* this_ptr, unsigned __int64 _Count, const void* _Val)
{
    __int64* v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-38h]@1
    __int64 v6; // [sp+20h] [bp-18h]@4
    void* v7; // [sp+28h] [bp-10h]@5
    AsyncLogVector* v8; // [sp+40h] [bp+8h]@1
    unsigned __int64 _Capacity; // [sp+48h] [bp+10h]@1
    const void* _Vala; // [sp+50h] [bp+18h]@1

    _Vala = _Val;
    _Capacity = _Count;
    v8 = (AsyncLogVector*)this_ptr;
    v3 = &v5;
    
    // Initialize debug pattern in local memory
    for (i = 12LL; i; --i) {
        *(DWORD*)v3 = 0xCCCCCCCC;
        v3 = (__int64*)((char*)v3 + 4);
    }
    
    v6 = -2LL;
    
    // Buy storage for the vector
    if (AsyncLogVectorBuy(v8, _Count)) {
        // Fill the allocated space with the given value
        v7 = AsyncLogVectorUfill(
            v8,
            v8->_Myfirst,
            _Capacity,
            _Vala);
        v8->_Mylast = v7;
    }
}

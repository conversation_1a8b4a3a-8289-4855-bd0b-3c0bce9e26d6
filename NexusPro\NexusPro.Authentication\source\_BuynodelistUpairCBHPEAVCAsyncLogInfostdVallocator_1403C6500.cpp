/**
 * @file AsyncLogListBuyNodeWithValue.cpp
 * @brief RF Online Async Log List Node Purchase/Allocation Function with Value
 * @note Original Function: ?_Buynode@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAAPEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@PEAU342@0AEBU?$std::pair@$$CBHPEAVCAsyncLogInfo@@@2@@Z
 * @note Original Address: 0x1403C6500
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: _BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6500.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <list>
#include <memory>
#include <utility>

/**
 * @brief Allocates and initializes a new list node with specified value and links
 * @param this Pointer to the async log list instance
 * @param _Next Pointer to the next node in the list
 * @param _Prev Pointer to the previous node in the list
 * @param _Val Pointer to the value to store in the new node
 * @return Pointer to the newly allocated and initialized list node
 *
 * This function allocates memory for a new list node, initializes its next and previous
 * pointers, and constructs the value in place. It's part of the STL list implementation
 * for managing doubly-linked list nodes with values.
 */
AsyncLogListNode* AsyncLogList_BuyNodeWithValue(
    AsyncLogList* this,
    AsyncLogListNode* _Next,
    AsyncLogListNode* _Prev,
    AsyncLogPair* _Val) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;               // Original: v4 (rdi register)
    signed __int64 loopCounter;           // Original: i (rcx register)
    AsyncLogListNode* nextNodePtr;        // Original: v6 (rdx register)
    AsyncLogListNode* prevNodePtr;        // Original: v7 (rdx register)
    AsyncLogListNode* valueNodePtr;       // Original: v8 (rdx register)
    __int64 stackBuffer[20];              // Original: v10 (stack buffer [sp+0h] [bp-58h])
    AsyncLogListNode* newNode;            // Original: v11 ([sp+20h] [bp-38h])
    int constructionFlags;                // Original: v12 ([sp+28h] [bp-30h])
    __int64 framePointer;                 // Original: v13 ([sp+30h] [bp-28h])
    AsyncLogListNode** nextNodePtrPtr;    // Original: _Ptr ([sp+38h] [bp-20h])
    AsyncLogListNode** prevNodePtrPtr;    // Original: v15 ([sp+40h] [bp-18h])
    AsyncLogPair* valuePtr;               // Original: v16 ([sp+48h] [bp-10h])
    AsyncLogList* currentList;            // Original: v17 ([sp+60h] [bp+8h])
    AsyncLogListNode* nextNode;           // Original: _Vala ([sp+68h] [bp+10h])
    AsyncLogListNode* prevNode;           // Original: v19 ([sp+70h] [bp+18h])
    AsyncLogPair* valueToStore;           // Original: v20 ([sp+78h] [bp+20h])

    // Initialize parameters
    valueToStore = _Val;
    prevNode = _Prev;
    nextNode = _Next;
    currentList = this;

    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;

        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }

    // Initialize frame pointer
    framePointer = -2LL;

    // Allocate memory for a new list node using the node allocator
    newNode = reinterpret_cast<AsyncLogListNode*>(
        AsyncLogListNodeAllocator_Allocate(&currentList->_Alnod, 1ui64)
    );

    // Initialize construction flags
    constructionFlags = 0;

    // Get pointer to the next node pointer field and construct it
    nextNodePtrPtr = AsyncLogList_GetNextNodePtr(newNode, nextNodePtr);
    AsyncLogListNodePtrAllocator_Construct(
        &currentList->_Alptr,
        nextNodePtrPtr,
        &nextNode
    );

    // Increment construction flags to track successful construction
    ++constructionFlags;

    // Get pointer to the previous node pointer field and construct it
    prevNodePtrPtr = AsyncLogList_GetPrevNodePtr(newNode, prevNodePtr);
    AsyncLogListNodePtrAllocator_Construct(
        &currentList->_Alptr,
        prevNodePtrPtr,
        &prevNode
    );

    // Increment construction flags to track successful construction
    ++constructionFlags;

    // Get pointer to the value field and construct the value in place
    valuePtr = AsyncLogList_GetValuePtr(newNode, valueNodePtr);
    AsyncLogPairAllocator_Construct(&currentList->_Alval, valuePtr, valueToStore);

    return newNode;
}




